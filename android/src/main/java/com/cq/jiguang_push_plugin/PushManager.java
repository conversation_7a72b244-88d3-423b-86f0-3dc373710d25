package com.cq.jiguang_push_plugin;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import cn.jpush.android.api.JPushInterface;
import cn.jpush.android.data.JPushConfig;

import java.util.Set;

/**
 * 极光推送管理器
 * 用于初始化和管理极光推送相关功能
 */
public class PushManager {
    private static final String TAG = "PushManager";
    private static PushManager instance;
    private Context context;
    private boolean isInitialized = false;

    private PushManager() {
    }

    /**
     * 获取单例实例
     */
    public static synchronized PushManager getInstance() {
        if (instance == null) {
            instance = new PushManager();
        }
        return instance;
    }

    /**
     * 初始化极光推送
     *
     * @param context 应用上下文
     */
    public void init(Context context) {
        if (isInitialized) {
            Log.d(TAG, "JPush already initialized");
            return;
        }

        this.context = context.getApplicationContext();
        
        try {
            // 创建JPush配置
            JPushInterface.setDebugMode(true);
            // 初始化JPush
            JPushInterface.init(context);
            
            isInitialized = true;
            Log.d(TAG, "JPush initialized successfully");
            
            // 获取注册ID
            String registrationId = JPushInterface.getRegistrationID(context);
            if (!TextUtils.isEmpty(registrationId)) {
                Log.d(TAG, "Registration ID: " + registrationId);
            } else {
                Log.d(TAG, "Registration ID is empty, will be available after initialization");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize JPush", e);
        }
    }

    /**
     * 获取注册ID
     *
     * @return 注册ID
     */
    public String getRegistrationId() {
        if (!isInitialized || context == null) {
            Log.w(TAG, "JPush not initialized");
            return "";
        }
        return JPushInterface.getRegistrationID(context);
    }

    /**
     * 设置别名
     *
     * @param alias    别名
     * @param sequence 请求序列号
     */
    public void setAlias(String alias, int sequence) {
        if (!isInitialized || context == null) {
            Log.w(TAG, "JPush not initialized");
            return;
        }
        
        if (TextUtils.isEmpty(alias)) {
            Log.w(TAG, "Alias is empty");
            return;
        }
        
        JPushInterface.setAlias(context, sequence, alias);
        Log.d(TAG, "Set alias: " + alias + ", sequence: " + sequence);
    }

    /**
     * 删除别名
     *
     * @param sequence 请求序列号
     */
    public void deleteAlias(int sequence) {
        if (!isInitialized || context == null) {
            Log.w(TAG, "JPush not initialized");
            return;
        }
        
        JPushInterface.deleteAlias(context, sequence);
        Log.d(TAG, "Delete alias, sequence: " + sequence);
    }

    /**
     * 设置标签
     *
     * @param tags     标签集合
     * @param sequence 请求序列号
     */
    public void setTags(Set<String> tags, int sequence) {
        if (!isInitialized || context == null) {
            Log.w(TAG, "JPush not initialized");
            return;
        }
        
        if (tags == null || tags.isEmpty()) {
            Log.w(TAG, "Tags is empty");
            return;
        }
        
        JPushInterface.setTags(context, sequence, tags);
        Log.d(TAG, "Set tags: " + tags.toString() + ", sequence: " + sequence);
    }

    /**
     * 添加标签
     *
     * @param tags     要添加的标签集合
     * @param sequence 请求序列号
     */
    public void addTags(Set<String> tags, int sequence) {
        if (!isInitialized || context == null) {
            Log.w(TAG, "JPush not initialized");
            return;
        }
        
        if (tags == null || tags.isEmpty()) {
            Log.w(TAG, "Tags is empty");
            return;
        }
        
        JPushInterface.addTags(context, sequence, tags);
        Log.d(TAG, "Add tags: " + tags.toString() + ", sequence: " + sequence);
    }

    /**
     * 删除标签
     *
     * @param tags     要删除的标签集合
     * @param sequence 请求序列号
     */
    public void deleteTags(Set<String> tags, int sequence) {
        if (!isInitialized || context == null) {
            Log.w(TAG, "JPush not initialized");
            return;
        }
        
        if (tags == null || tags.isEmpty()) {
            Log.w(TAG, "Tags is empty");
            return;
        }
        
        JPushInterface.deleteTags(context, sequence, tags);
        Log.d(TAG, "Delete tags: " + tags.toString() + ", sequence: " + sequence);
    }

    /**
     * 清除所有标签
     *
     * @param sequence 请求序列号
     */
    public void cleanTags(int sequence) {
        if (!isInitialized || context == null) {
            Log.w(TAG, "JPush not initialized");
            return;
        }
        
        JPushInterface.cleanTags(context, sequence);
        Log.d(TAG, "Clean all tags, sequence: " + sequence);
    }

    /**
     * 获取所有标签
     *
     * @param sequence 请求序列号
     */
    public void getAllTags(int sequence) {
        if (!isInitialized || context == null) {
            Log.w(TAG, "JPush not initialized");
            return;
        }
        
        JPushInterface.getAllTags(context, sequence);
        Log.d(TAG, "Get all tags, sequence: " + sequence);
    }

    /**
     * 停止推送服务
     */
    public void stopPush() {
        if (!isInitialized || context == null) {
            Log.w(TAG, "JPush not initialized");
            return;
        }
        
        JPushInterface.stopPush(context);
        Log.d(TAG, "Stop push service");
    }

    /**
     * 恢复推送服务
     */
    public void resumePush() {
        if (!isInitialized || context == null) {
            Log.w(TAG, "JPush not initialized");
            return;
        }
        
        JPushInterface.resumePush(context);
        Log.d(TAG, "Resume push service");
    }

    /**
     * 检查推送服务是否停止
     *
     * @return true表示已停止，false表示正在运行
     */
    public boolean isPushStopped() {
        if (!isInitialized || context == null) {
            Log.w(TAG, "JPush not initialized");
            return true;
        }
        
        return JPushInterface.isPushStopped(context);
    }

    /**
     * 检查是否已初始化
     *
     * @return 是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }

    /**
     * 销毁资源
     */
    public void destroy() {
        context = null;
        isInitialized = false;
        Log.d(TAG, "PushManager destroyed");
    }
}
