package com.cq.jiguang_push_plugin;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import androidx.annotation.NonNull;

import cn.jpush.android.api.JPushInterface;
import cn.jpush.android.data.JPushConfig;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;
import io.flutter.plugin.common.PluginRegistry;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * JiguangPushPlugin
 */
public class JiguangPushPlugin implements FlutterPlugin, MethodCallHandler, ActivityAware, PluginRegistry.NewIntentListener {
    private static final String TAG = "JiguangPushPlugin";
    private static final String PUSH_ACTION = "com.changqing.health";

    /// The MethodChannel that will the communication between Flutter and native Android
    ///
    /// This local reference serves to register the plugin with the Flutter Engine and unregister it
    /// when the Flutter Engine is detached from the Activity
    private MethodChannel channel;
    private Context context;
    private PushManager pushManager;

    private ActivityPluginBinding activityBinding;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        context = flutterPluginBinding.getApplicationContext();
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "jiguang_push_plugin");
        channel.setMethodCallHandler(this);

        // 初始化推送管理器
        pushManager = PushManager.getInstance();
        pushManager.init(context);

        Log.d(TAG, "JiguangPushPlugin attached to engine");
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        try {
            switch (call.method) {
                case "getPlatformVersion":
                    result.success("Android " + android.os.Build.VERSION.RELEASE);
                    break;

                case "initPush":
                    handleInitPush(result);
                    break;

                case "getRegistrationId":
                    handleGetRegistrationId(result);
                    break;

                case "setAlias":
                    handleSetAlias(call, result);
                    break;

                case "deleteAlias":
                    handleDeleteAlias(call, result);
                    break;

                case "setTags":
                    handleSetTags(call, result);
                    break;

                case "addTags":
                    handleAddTags(call, result);
                    break;

                case "deleteTags":
                    handleDeleteTags(call, result);
                    break;

                case "cleanTags":
                    handleCleanTags(call, result);
                    break;

                case "getAllTags":
                    handleGetAllTags(call, result);
                    break;

                case "stopPush":
                    handleStopPush(result);
                    break;

                case "resumePush":
                    handleResumePush(result);
                    break;

                case "isPushStopped":
                    handleIsPushStopped(result);
                    break;

                default:
                    result.notImplemented();
                    break;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling method call: " + call.method, e);
            result.error("ERROR", "Failed to handle method call: " + e.getMessage(), null);
        }
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        channel.setMethodCallHandler(null);
        if (pushManager != null) {
            pushManager.destroy();
        }
        Log.d(TAG, "JiguangPushPlugin detached from engine");
    }

    /**
     * 处理初始化推送
     */
    private void handleInitPush(Result result) {
        if (pushManager != null) {
            pushManager.init(context);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Push initialized successfully");
            result.success(response);
        } else {
            result.error("INIT_ERROR", "PushManager is null", null);
        }
    }

    /**
     * 处理获取注册ID
     */
    private void handleGetRegistrationId(Result result) {
        if (pushManager != null) {
            String registrationId = pushManager.getRegistrationId();
            result.success(registrationId);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理设置别名
     */
    private void handleSetAlias(MethodCall call, Result result) {
        String alias = call.argument("alias");
        Integer sequence = call.argument("sequence");

        if (alias == null) {
            result.error("INVALID_ARGUMENT", "Alias cannot be null", null);
            return;
        }

        if (sequence == null) {
            sequence = 1;
        }

        if (pushManager != null) {
            pushManager.setAlias(alias, sequence);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sequence", sequence);
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理删除别名
     */
    private void handleDeleteAlias(MethodCall call, Result result) {
        Integer sequence = call.argument("sequence");

        if (sequence == null) {
            sequence = 1;
        }

        if (pushManager != null) {
            pushManager.deleteAlias(sequence);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sequence", sequence);
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理设置标签
     */
    private void handleSetTags(MethodCall call, Result result) {
        List<String> tagList = call.argument("tags");
        Integer sequence = call.argument("sequence");

        if (tagList == null || tagList.isEmpty()) {
            result.error("INVALID_ARGUMENT", "Tags cannot be null or empty", null);
            return;
        }

        if (sequence == null) {
            sequence = 1;
        }

        Set<String> tags = new HashSet<>(tagList);

        if (pushManager != null) {
            pushManager.setTags(tags, sequence);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sequence", sequence);
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理添加标签
     */
    private void handleAddTags(MethodCall call, Result result) {
        List<String> tagList = call.argument("tags");
        Integer sequence = call.argument("sequence");

        if (tagList == null || tagList.isEmpty()) {
            result.error("INVALID_ARGUMENT", "Tags cannot be null or empty", null);
            return;
        }

        if (sequence == null) {
            sequence = 1;
        }

        Set<String> tags = new HashSet<>(tagList);

        if (pushManager != null) {
            pushManager.addTags(tags, sequence);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sequence", sequence);
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理删除标签
     */
    private void handleDeleteTags(MethodCall call, Result result) {
        List<String> tagList = call.argument("tags");
        Integer sequence = call.argument("sequence");

        if (tagList == null || tagList.isEmpty()) {
            result.error("INVALID_ARGUMENT", "Tags cannot be null or empty", null);
            return;
        }

        if (sequence == null) {
            sequence = 1;
        }

        Set<String> tags = new HashSet<>(tagList);

        if (pushManager != null) {
            pushManager.deleteTags(tags, sequence);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sequence", sequence);
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理清除所有标签
     */
    private void handleCleanTags(MethodCall call, Result result) {
        Integer sequence = call.argument("sequence");

        if (sequence == null) {
            sequence = 1;
        }

        if (pushManager != null) {
            pushManager.cleanTags(sequence);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sequence", sequence);
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理获取所有标签
     */
    private void handleGetAllTags(MethodCall call, Result result) {
        Integer sequence = call.argument("sequence");

        if (sequence == null) {
            sequence = 1;
        }

        if (pushManager != null) {
            pushManager.getAllTags(sequence);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sequence", sequence);
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理停止推送
     */
    private void handleStopPush(Result result) {
        if (pushManager != null) {
            pushManager.stopPush();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Push service stopped");
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理恢复推送
     */
    private void handleResumePush(Result result) {
        if (pushManager != null) {
            pushManager.resumePush();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Push service resumed");
            result.success(response);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    /**
     * 处理检查推送状态
     */
    private void handleIsPushStopped(Result result) {
        if (pushManager != null) {
            boolean isStopped = pushManager.isPushStopped();
            result.success(isStopped);
        } else {
            result.error("MANAGER_NULL", "PushManager is null", null);
        }
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
        activityBinding = binding;
        Activity activity = binding.getActivity();
        Intent intent = activity.getIntent();
        Log.e(TAG, "onAttachedToActivity: " + intent.getAction());
        if (PUSH_ACTION.equals(intent.getAction())) {
            // TODO 处理推送意图
            handlePushIntent(intent);
        }
        binding.addOnNewIntentListener(this);
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {
        if (activityBinding != null) {
            activityBinding.removeOnNewIntentListener(this);
        }
    }

    @Override
    public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {
        activityBinding = binding;
        binding.addOnNewIntentListener(this);
    }

    @Override
    public void onDetachedFromActivity() {
        if (activityBinding != null) {
            activityBinding.removeOnNewIntentListener(this);
            activityBinding = null;
        }
    }

    /**
     * 处理新的意图
     *
     * @param intent The new intent that was started for the activity.
     * @return
     */
    @Override
    public boolean onNewIntent(@NonNull Intent intent) {
        if (PUSH_ACTION.equals(intent.getAction())) {
            handlePushIntent(intent);
        }
        return true;
    }

    /**
     * 处理推送意图
     *
     * @param intent
     */
    private void handlePushIntent(Intent intent) {
        // TODO 处理推送意图，推送消息到 Dart
        String value1 = intent.getStringExtra("key1");
        int value2 = intent.getIntExtra("key2", 0);
        Log.e(TAG, "handlePushIntent: value1: " + value1 + ", value2: " + value2);
    }
}
