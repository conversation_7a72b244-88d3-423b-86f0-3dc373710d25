# 类型安全使用指南

## 问题说明

在Flutter方法通道中，Android原生端返回的`Map<String, Object>`类型在Dart端会被接收为`Map<Object?, Object?>`类型，直接进行类型转换会导致运行时错误：

```
Map<Object?, Object?> is not a subtype of type Map<String, dynamic>? in type cast
```

## 解决方案

我们已经在`MethodChannelJiguangPushPlugin`中实现了安全的类型转换：

### 1. 安全转换方法

```dart
/// 安全地将原生返回的Map转换为Map<String, dynamic>
Map<String, dynamic>? _safeMapConversion(dynamic result) {
  if (result == null) return null;
  if (result is Map) {
    return Map<String, dynamic>.from(result);
  }
  return null;
}
```

### 2. 方法实现

```dart
@override
Future<Map<String, dynamic>?> initPush() async {
  final result = await methodChannel.invokeMethod('initPush');
  return _safeMapConversion(result);
}
```

## 正确的使用方式

### ✅ 推荐的使用方式

```dart
Future<void> initializePush() async {
  try {
    final result = await JiguangPushPlugin.initPush();
    
    // 安全的null检查和类型检查
    if (result != null && result['success'] == true) {
      print('推送初始化成功: ${result['message'] ?? ''}');
    } else {
      print('推送初始化失败: ${result?['message'] ?? '未知错误'}');
    }
  } catch (e) {
    print('初始化异常: $e');
  }
}
```

### ❌ 避免的使用方式

```dart
// 不安全的直接访问
final result = await JiguangPushPlugin.initPush();
print(result['success']); // 可能导致null异常

// 不安全的类型假设
if (result?['success'] == true) { // 这样是安全的
  // ...
}
```

## 各方法的安全使用示例

### 1. initPush() - 初始化推送

```dart
Future<void> initPush() async {
  try {
    final result = await JiguangPushPlugin.initPush();
    if (result != null && result['success'] == true) {
      print('初始化成功: ${result['message']}');
    } else {
      print('初始化失败: ${result?['message'] ?? '未知错误'}');
    }
  } catch (e) {
    print('异常: $e');
  }
}
```

### 2. getRegistrationId() - 获取注册ID

```dart
Future<String?> getRegistrationId() async {
  try {
    final registrationId = await JiguangPushPlugin.getRegistrationId();
    if (registrationId != null && registrationId.isNotEmpty) {
      print('注册ID: $registrationId');
      return registrationId;
    } else {
      print('获取注册ID失败');
      return null;
    }
  } catch (e) {
    print('获取注册ID异常: $e');
    return null;
  }
}
```

### 3. stopPush() - 停止推送

```dart
Future<bool> stopPush() async {
  try {
    final result = await JiguangPushPlugin.stopPush();
    if (result != null && result['success'] == true) {
      print('推送已停止: ${result['message']}');
      return true;
    } else {
      print('停止推送失败: ${result?['message'] ?? '未知错误'}');
      return false;
    }
  } catch (e) {
    print('停止推送异常: $e');
    return false;
  }
}
```

### 4. resumePush() - 恢复推送

```dart
Future<bool> resumePush() async {
  try {
    final result = await JiguangPushPlugin.resumePush();
    if (result != null && result['success'] == true) {
      print('推送已恢复: ${result['message']}');
      return true;
    } else {
      print('恢复推送失败: ${result?['message'] ?? '未知错误'}');
      return false;
    }
  } catch (e) {
    print('恢复推送异常: $e');
    return false;
  }
}
```

### 5. isPushStopped() - 检查推送状态

```dart
Future<bool> checkPushStatus() async {
  try {
    final isStopped = await JiguangPushPlugin.isPushStopped();
    if (isStopped != null) {
      print('推送状态: ${isStopped ? "已停止" : "运行中"}');
      return isStopped;
    } else {
      print('无法获取推送状态');
      return true; // 默认认为已停止
    }
  } catch (e) {
    print('检查推送状态异常: $e');
    return true; // 默认认为已停止
  }
}
```

## 完整的使用示例

```dart
class PushService {
  static Future<void> initializeAndSetup() async {
    // 1. 初始化推送
    final initResult = await JiguangPushPlugin.initPush();
    if (initResult != null && initResult['success'] == true) {
      print('✅ 推送初始化成功');
      
      // 2. 获取注册ID
      final registrationId = await JiguangPushPlugin.getRegistrationId();
      if (registrationId != null && registrationId.isNotEmpty) {
        print('✅ 注册ID: $registrationId');
        
        // 3. 检查推送状态
        final isStopped = await JiguangPushPlugin.isPushStopped();
        print('📊 推送状态: ${isStopped == true ? "已停止" : "运行中"}');
        
        // 4. 如果需要，可以控制推送服务
        if (isStopped == true) {
          final resumeResult = await JiguangPushPlugin.resumePush();
          if (resumeResult != null && resumeResult['success'] == true) {
            print('✅ 推送服务已恢复');
          }
        }
      } else {
        print('❌ 获取注册ID失败');
      }
    } else {
      print('❌ 推送初始化失败: ${initResult?['message'] ?? '未知错误'}');
    }
  }
}
```

## 注意事项

1. **始终进行null检查**: 所有方法都可能返回null
2. **检查success字段**: 对于返回Map的方法，检查success字段确认操作是否成功
3. **使用安全的访问操作符**: 使用`?.`和`??`操作符进行安全访问
4. **异常处理**: 使用try-catch包装所有调用
5. **提供默认值**: 为可能为null的值提供合理的默认值

通过遵循这些指南，您可以安全地使用极光推送插件的所有功能，避免类型转换错误。
