import 'package:flutter/material.dart';
import 'package:jiguang_push_plugin/jiguang_push_plugin.dart';

/// 极光推送使用示例
class PushExample extends StatefulWidget {
  const PushExample({Key? key}) : super(key: key);

  @override
  State<PushExample> createState() => _PushExampleState();
}

class _PushExampleState extends State<PushExample> {
  String _registrationId = '未获取';
  bool _isPushStopped = false;
  String _statusMessage = '准备就绪';

  @override
  void initState() {
    super.initState();
    _initializePush();
  }

  /// 初始化推送服务
  Future<void> _initializePush() async {
    try {
      final result = await JiguangPushPlugin.initPush();
      if (result != null && result['success'] == true) {
        setState(() {
          _statusMessage = '推送初始化成功: ${result['message'] ?? ''}';
        });
        _getRegistrationId();
        _checkPushStatus();
      } else {
        setState(() {
          _statusMessage = '推送初始化失败: ${result?['message'] ?? '未知错误'}';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '初始化异常: $e';
      });
    }
  }

  /// 获取注册ID
  Future<void> _getRegistrationId() async {
    try {
      final registrationId = await JiguangPushPlugin.getRegistrationId();
      setState(() {
        _registrationId = registrationId ?? '获取失败';
      });
    } catch (e) {
      setState(() {
        _registrationId = '获取异常: $e';
      });
    }
  }

  /// 停止推送服务
  Future<void> _stopPush() async {
    try {
      final result = await JiguangPushPlugin.stopPush();
      if (result != null && result['success'] == true) {
        setState(() {
          _statusMessage = '推送服务已停止: ${result['message'] ?? ''}';
        });
        _checkPushStatus();
      } else {
        setState(() {
          _statusMessage = '停止推送失败: ${result?['message'] ?? '未知错误'}';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '停止推送异常: $e';
      });
    }
  }

  /// 恢复推送服务
  Future<void> _resumePush() async {
    try {
      final result = await JiguangPushPlugin.resumePush();
      if (result != null && result['success'] == true) {
        setState(() {
          _statusMessage = '推送服务已恢复: ${result['message'] ?? ''}';
        });
        _checkPushStatus();
      } else {
        setState(() {
          _statusMessage = '恢复推送失败: ${result?['message'] ?? '未知错误'}';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '恢复推送异常: $e';
      });
    }
  }

  /// 检查推送状态
  Future<void> _checkPushStatus() async {
    try {
      final isStopped = await JiguangPushPlugin.isPushStopped();
      setState(() {
        _isPushStopped = isStopped ?? true;
      });
    } catch (e) {
      setState(() {
        _statusMessage = '检查状态异常: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('极光推送示例'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 状态信息卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '推送状态',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text('状态: $_statusMessage'),
                    const SizedBox(height: 8),
                    Text('推送服务: ${_isPushStopped ? "已停止" : "运行中"}'),
                    const SizedBox(height: 8),
                    Text('注册ID: $_registrationId'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            
            // 操作按钮
            ElevatedButton(
              onPressed: _initializePush,
              child: const Text('初始化推送'),
            ),
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _getRegistrationId,
              child: const Text('获取注册ID'),
            ),
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _isPushStopped ? null : _stopPush,
              child: const Text('停止推送'),
            ),
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _isPushStopped ? _resumePush : null,
              child: const Text('恢复推送'),
            ),
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: _checkPushStatus,
              child: const Text('检查推送状态'),
            ),
            
            const SizedBox(height: 20),
            
            // 使用说明
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '使用说明',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text('1. 首先点击"初始化推送"来启动推送服务'),
                    const Text('2. 获取注册ID用于服务端推送时指定设备'),
                    const Text('3. 可以停止和恢复推送服务'),
                    const Text('4. 检查推送状态了解当前服务状态'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
