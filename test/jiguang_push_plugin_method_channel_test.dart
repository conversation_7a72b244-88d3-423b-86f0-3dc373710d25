import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jiguang_push_plugin/jiguang_push_plugin_method_channel.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  MethodChannelJiguangPushPlugin platform = MethodChannelJiguangPushPlugin();
  const MethodChannel channel = MethodChannel('jiguang_push_plugin');

  setUp(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'getPlatformVersion':
            return '42';
          case 'initPush':
          case 'stopPush':
          case 'resumePush':
            return {
              'success': true,
              'message': 'Operation successful'
            };
          case 'getRegistrationId':
            return 'test_registration_id_12345';
          case 'isPushStopped':
            return false;
          default:
            return null;
        }
      },
    );
  });

  tearDown(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(channel, null);
  });

  test('getPlatformVersion', () async {
    expect(await platform.getPlatformVersion(), '42');
  });

  test('initPush', () async {
    final result = await platform.initPush();
    expect(result?['success'], true);
    expect(result?['message'], 'Operation successful');
  });

  test('stopPush', () async {
    final result = await platform.stopPush();
    expect(result?['success'], true);
    expect(result?['message'], 'Operation successful');
  });

  test('resumePush', () async {
    final result = await platform.resumePush();
    expect(result?['success'], true);
    expect(result?['message'], 'Operation successful');
  });

  test('getRegistrationId', () async {
    final result = await platform.getRegistrationId();
    expect(result, 'test_registration_id_12345');
  });

  test('isPushStopped', () async {
    final result = await platform.isPushStopped();
    expect(result, false);
  });
}
