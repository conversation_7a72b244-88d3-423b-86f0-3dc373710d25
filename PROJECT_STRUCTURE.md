# 项目结构说明

## 正确的依赖管理架构

### 📁 插件项目 (`/android/`)
**这是插件的核心实现，应该包含所有推送相关的依赖**

#### `/android/build.gradle` - 插件项目构建文件
```gradle
dependencies {
    // 极光推送核心依赖
    implementation 'cn.jiguang.sdk:jpush:5.8.0'

    // 厂商推送依赖
    implementation 'cn.jiguang.sdk.plugin:xiaomi:5.8.0'
    implementation 'cn.jiguang.sdk.plugin:huawei:5.8.0'
    implementation 'cn.jiguang.sdk.plugin:vivo:5.8.0'
    implementation 'cn.jiguang.sdk.plugin:oppo:5.8.0'

    // 注意：插件项目不能直接依赖AAR文件
    // AAR依赖需要在使用插件的项目中配置

    // OPPO 额外依赖（可以在插件中配置）
    implementation 'com.google.code.gson:gson:2.11.0'
    implementation 'commons-codec:commons-codec:1.11'
    implementation 'androidx.annotation:annotation:1.9.1'
}
```

#### 重要说明
**插件项目不能依赖AAR文件**，因为插件本身就是要被打包成AAR的。AAR依赖必须在使用插件的项目中配置。

#### `/android/src/main/java/` - 插件的Java/Kotlin代码
```
android/src/main/java/com/cq/jiguang_push_plugin/
├── JiguangPushPlugin.java      // 主插件类
├── PushManager.java            // 推送管理器
└── PushMessageReceiver.java    // 消息接收器
```

---

### 📁 示例项目 (`/example/android/`)
**这是演示如何使用插件的示例应用，不应该重复依赖推送相关的库**

#### `/example/android/build.gradle` - 示例项目的外层构建文件
```gradle
// 这个文件主要配置构建脚本和仓库
// 不应该包含推送相关的依赖

dependencies {
    // 只包含测试相关的依赖
    testImplementation("junit:junit:4.13.2")
    testImplementation("org.mockito:mockito-core:5.0.0")
    
    // 注意：推送相关的依赖应该在插件项目的 android/build.gradle 中配置
    // 示例项目不需要重复依赖，会通过插件自动获得
}
```

#### `/example/android/app/build.gradle` - 示例应用的构建文件
```gradle
android {
    defaultConfig {
        applicationId = "com.changqing.health"

        // 配置极光推送的参数
        manifestPlaceholders = [
            JPUSH_PKGNAME : applicationId,
            JPUSH_APPKEY  : "fb14eb189466ab13e928e7ae",
            JPUSH_CHANNEL : "developer-default",

            // 厂商推送配置
            XIAOMI_APPID  : "MI-小米的APPID",
            XIAOMI_APPKEY : "MI-小米的APPKEY",
            OPPO_APPKEY   : "OP-您的应用对应的OPPO的APPKEY",
            OPPO_APPID    : "OP-您的应用对应的OPPO的APPID",
            OPPO_APPSECRET: "OP-您的应用对应的OPPO的APPSECRET",
            VIVO_APPKEY   : "e3ba1a2c19634cbb8bd8fbe7c7e79761",
            VIVO_APPID    : "105916775",
            // ... 其他厂商配置
        ]
    }
}

repositories {
    flatDir {
        dirs 'libs'
    }
}
dependencies {
    // OPPO AAR 依赖 - 插件无法直接依赖AAR，需要在使用插件的项目中配置
    implementation(name: 'com.heytap.msp_3.5.3', ext: 'aar')
}
```

#### `/example/android/app/libs/` - 示例应用的本地依赖
```
example/android/app/libs/
└── com.heytap.msp_3.5.3.aar  // OPPO推送AAR文件
```

---

## 为什么要这样设计？

### ✅ 正确的架构优势

1. **避免重复依赖**: 推送相关的库只在插件项目中声明一次
2. **版本一致性**: 所有使用插件的项目都使用相同版本的推送库
3. **维护简单**: 只需要在插件项目中更新推送库版本
4. **打包正确**: 插件会自动将依赖打包到AAR中
5. **使用方便**: 使用插件的项目只需要添加插件依赖即可

### ❌ 错误架构的问题

如果在示例项目中重复依赖推送库：

1. **依赖冲突**: 可能导致版本冲突
2. **重复打包**: 相同的库被打包多次
3. **维护困难**: 需要在多个地方同步更新版本
4. **构建错误**: 可能导致构建失败或运行时错误

---

## 使用插件的正确方式

### 对于插件使用者

在你的Flutter项目中，只需要：

1. **在 `pubspec.yaml` 中添加插件依赖**:
```yaml
dependencies:
  jiguang_push_plugin: ^1.0.0
```

2. **在 `android/app/build.gradle` 中配置参数**:
```gradle
android {
    defaultConfig {
        manifestPlaceholders = [
            JPUSH_PKGNAME : applicationId,
            JPUSH_APPKEY  : "你的AppKey",
            JPUSH_CHANNEL : "developer-default",
        ]
    }
}
```

3. **不需要手动添加任何推送相关的依赖** - 插件会自动提供！

---

## 总结

- **插件项目** (`/android/`) = 包含所有推送依赖的地方
- **示例项目** (`/example/android/`) = 演示如何使用插件，不重复依赖
- **使用者项目** = 只需要添加插件依赖和配置参数

这样的架构确保了依赖管理的清晰性和项目的可维护性。
