import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'jiguang_push_plugin_method_channel.dart';

abstract class JiguangPushPluginPlatform extends PlatformInterface {
  /// Constructs a JiguangPushPluginPlatform.
  JiguangPushPluginPlatform() : super(token: _token);

  static final Object _token = Object();

  static JiguangPushPluginPlatform _instance = MethodChannelJiguangPushPlugin();

  /// The default instance of [JiguangPushPluginPlatform] to use.
  ///
  /// Defaults to [MethodChannelJiguangPushPlugin].
  static JiguangPushPluginPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [JiguangPushPluginPlatform] when
  /// they register themselves.
  static set instance(JiguangPushPluginPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  /// 初始化极光推送
  Future<Map<String, dynamic>?> initPush() {
    throw UnimplementedError('initPush() has not been implemented.');
  }

  /// 停止推送服务
  Future<Map<String, dynamic>?> stopPush() {
    throw UnimplementedError('stopPush() has not been implemented.');
  }

  /// 恢复推送服务
  Future<Map<String, dynamic>?> resumePush() {
    throw UnimplementedError('resumePush() has not been implemented.');
  }

  /// 获取注册ID
  Future<String?> getRegistrationId() {
    throw UnimplementedError('getRegistrationId() has not been implemented.');
  }

  /// 检查推送服务是否停止
  Future<bool?> isPushStopped() {
    throw UnimplementedError('isPushStopped() has not been implemented.');
  }
}
