import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'jiguang_push_plugin_platform_interface.dart';

/// An implementation of [JiguangPushPluginPlatform] that uses method channels.
class MethodChannelJiguangPushPlugin extends JiguangPushPluginPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('jiguang_push_plugin');

  /// 安全地将原生返回的Map转换为Map<String, dynamic>
  Map<String, dynamic>? _safeMapConversion(dynamic result) {
    if (result == null) return null;
    if (result is Map) {
      return Map<String, dynamic>.from(result);
    }
    return null;
  }

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }

  @override
  Future<Map<String, dynamic>?> initPush() async {
    final result = await methodChannel.invokeMethod('initPush');
    return _safeMapConversion(result);
  }

  @override
  Future<Map<String, dynamic>?> stopPush() async {
    final result = await methodChannel.invokeMethod('stopPush');
    return _safeMapConversion(result);
  }

  @override
  Future<Map<String, dynamic>?> resumePush() async {
    final result = await methodChannel.invokeMethod('resumePush');
    return _safeMapConversion(result);
  }

  @override
  Future<String?> getRegistrationId() async {
    final result = await methodChannel.invokeMethod('getRegistrationId');
    return result?.toString();
  }

  @override
  Future<bool?> isPushStopped() async {
    final result = await methodChannel.invokeMethod('isPushStopped');
    return result as bool?;
  }
}
