# 正确的插件架构设计

## 架构原则 ✅

**核心原则**: 所有推送相关的配置都应该在插件项目中，主项目只包含基本的Flutter配置和必要的参数配置。

## 插件项目配置 (`/android/`)

### `/android/settings.gradle` - 插件项目的设置
```gradle
pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        // 华为push - 在插件项目中配置
        maven { url 'https://developer.huawei.com/repo/' }
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_PROJECT)
    repositories {
        google()
        mavenCentral()
        // 华为push - 在插件项目中配置
        maven { url 'https://developer.huawei.com/repo/' }
    }
}

rootProject.name = 'jiguang_push_plugin'
```

### `/android/build.gradle` - 插件项目的构建配置
```gradle
buildscript {
    repositories {
        google()
        mavenCentral()
        // 华为push - 在插件项目中配置
        maven { url 'https://developer.huawei.com/repo/' }
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.1.0")
        // 华为push - 在插件项目中配置
        classpath 'com.huawei.agconnect:agcp:1.9.1.301'
    }
}

apply plugin: "com.android.library"
// 华为push - 在插件项目中应用
apply plugin: 'com.huawei.agconnect'

dependencies {
    // 所有推送相关的依赖都在插件项目中
    implementation 'cn.jiguang.sdk:jpush:5.8.0'
    implementation 'cn.jiguang.sdk.plugin:xiaomi:5.8.0'
    implementation 'cn.jiguang.sdk.plugin:huawei:5.8.0'
    implementation 'cn.jiguang.sdk.plugin:vivo:5.8.0'
    implementation 'cn.jiguang.sdk.plugin:oppo:5.8.0'
    
    // OPPO相关依赖
    implementation 'com.google.code.gson:gson:2.11.0'
    implementation 'commons-codec:commons-codec:1.11'
    implementation 'androidx.annotation:annotation:1.9.1'
}
```

## 示例项目配置 (`/example/android/`)

### `/example/android/settings.gradle` - 示例项目的设置
```gradle
pluginManagement {
    // 只包含基本的Flutter配置，不包含推送相关配置
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version '8.9.2' apply false
    id "org.jetbrains.kotlin.android" version "1.8.22" apply false
}
```

### `/example/android/build.gradle` - 示例项目的构建配置
```gradle
allprojects {
    repositories {
        google()
        mavenCentral()
        // 不包含推送相关的仓库配置
    }
}

// 其他基本配置...
```

### `/example/android/app/build.gradle` - 示例应用的配置
```gradle
android {
    defaultConfig {
        // 只包含推送参数配置，不包含推送依赖
        manifestPlaceholders = [
            JPUSH_PKGNAME : applicationId,
            JPUSH_APPKEY  : "fb14eb189466ab13e928e7ae",
            JPUSH_CHANNEL : "developer-default",
            
            // 厂商推送参数
            XIAOMI_APPID  : "MI-小米的APPID",
            XIAOMI_APPKEY : "MI-小米的APPKEY",
            OPPO_APPKEY   : "OP-OPPO的APPKEY",
            // ... 其他厂商参数
        ]
    }
}

dependencies {
    // 只包含必要的AAR依赖（因为插件无法包含AAR）
    implementation(name: 'com.heytap.msp_3.5.3', ext: 'aar')
}
```

## 使用者项目配置

### 对于使用插件的第三方项目

**只需要做两件事**：

1. **添加插件依赖**:
```yaml
# pubspec.yaml
dependencies:
  jiguang_push_plugin: ^1.0.0
```

2. **配置推送参数**:
```gradle
// android/app/build.gradle
android {
    defaultConfig {
        manifestPlaceholders = [
            JPUSH_PKGNAME : applicationId,
            JPUSH_APPKEY  : "你的AppKey",
            JPUSH_CHANNEL : "developer-default",
        ]
    }
}

dependencies {
    // 如果需要OPPO推送，添加AAR依赖
    implementation(name: 'com.heytap.msp_3.5.3', ext: 'aar')
}
```

**不需要做的事**：
- ❌ 不需要添加任何推送SDK依赖
- ❌ 不需要配置推送相关的仓库
- ❌ 不需要添加华为推送的classpath
- ❌ 不需要应用华为推送插件

## 架构优势

### ✅ 插件开发者的优势
1. **集中管理**: 所有推送配置都在插件项目中
2. **版本控制**: 统一管理所有推送SDK的版本
3. **依赖传递**: 插件会自动将依赖传递给使用者
4. **维护简单**: 只需要在一个地方更新推送SDK

### ✅ 插件使用者的优势
1. **使用简单**: 只需要添加插件依赖和配置参数
2. **无需关心**: 不需要了解推送SDK的具体依赖关系
3. **自动更新**: 插件更新时自动获得最新的推送SDK
4. **减少错误**: 避免了依赖冲突和配置错误

### ✅ 项目架构的优势
1. **职责分离**: 插件负责功能实现，项目负责参数配置
2. **依赖隔离**: 推送相关依赖被封装在插件中
3. **可维护性**: 清晰的边界使得维护更容易
4. **可扩展性**: 新增推送功能只需要修改插件

## 特殊情况处理

### AAR文件依赖
由于Flutter插件无法直接包含AAR文件，某些厂商的AAR依赖（如OPPO）仍需要在使用者项目中配置：

```gradle
// 使用者项目的 android/app/build.gradle
repositories {
    flatDir {
        dirs 'libs'
    }
}
dependencies {
    implementation(name: 'com.heytap.msp_3.5.3', ext: 'aar')
}
```

这是目前Flutter插件架构的限制，但这种情况很少见。

## 验证结果

✅ **构建成功**: Debug和Release版本都能正常构建  
✅ **依赖正确**: 所有推送依赖都通过插件传递  
✅ **配置清晰**: 插件和项目的职责边界明确  
✅ **使用简单**: 第三方使用者只需要最少的配置  

这种架构设计符合Flutter插件开发的最佳实践，确保了代码的可维护性和使用的便利性。
