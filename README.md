# 极光推送 Flutter 插件 (jiguang_push_plugin)

这是一个集成极光推送服务的 Flutter 插件，支持 Android 平台的推送功能。

## 功能特性

- ✅ 初始化极光推送服务
- ✅ 获取设备注册ID
- ✅ 停止推送服务
- ✅ 恢复推送服务
- ✅ 检查推送服务状态
- ✅ 设置别名和标签（原生端已实现）
- ✅ 接收推送消息和通知

## 安装

在 `pubspec.yaml` 文件中添加依赖：

```yaml
dependencies:
  jiguang_push_plugin: ^1.0.0
```

然后运行：

```bash
flutter pub get
```

## Android 配置

### 1. 添加权限

在 `android/app/src/main/AndroidManifest.xml` 中添加必要权限：

```xml
<!-- 必须权限 -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

<!-- 可选权限，用于提高推送精准度 -->
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
```

### 2. 配置 AppKey

在 `android/app/src/main/AndroidManifest.xml` 的 `<application>` 标签内添加：

```xml
<meta-data
    android:name="JPUSH_APPKEY"
    android:value="你的极光推送AppKey" />
<meta-data
    android:name="JPUSH_CHANNEL"
    android:value="developer-default" />
```

### 3. 配置消息接收器

创建自定义的消息接收器继承 `JPushMessageReceiver`，并在 AndroidManifest.xml 中注册：

```xml
<receiver
    android:name=".PushMessageReceiver"
    android:enabled="true"
    android:exported="false">
    <intent-filter>
        <action android:name="cn.jpush.android.intent.RECEIVER_MESSAGE" />
        <category android:name="你的应用包名" />
    </intent-filter>
</receiver>
```

### 4. 配置厂商AAR依赖（如需要）

**重要说明**：某些厂商推送（如OPPO）需要额外的AAR文件，这些文件无法在插件中直接依赖，需要在使用插件的项目中配置。

如果需要OPPO推送支持，请：

1. 下载OPPO推送AAR文件并放置到 `android/app/libs/` 目录
2. 在 `android/app/build.gradle` 中添加：

```gradle
repositories {
    flatDir {
        dirs 'libs'
    }
}
dependencies {
    implementation(name: 'com.heytap.msp_3.5.3', ext: 'aar')
}
```

## 使用方法

### 基础用法

```dart
import 'package:jiguang_push_plugin/jiguang_push_plugin.dart';

class PushService {
  /// 初始化推送服务
  static Future<void> initializePush() async {
    try {
      final result = await JiguangPushPlugin.initPush();
      if (result != null && result['success'] == true) {
        print('推送初始化成功: ${result['message'] ?? ''}');
      } else {
        print('推送初始化失败: ${result?['message'] ?? '未知错误'}');
      }
    } catch (e) {
      print('初始化异常: $e');
    }
  }

  /// 获取设备注册ID
  static Future<String?> getDeviceId() async {
    try {
      final registrationId = await JiguangPushPlugin.getRegistrationId();
      print('设备注册ID: $registrationId');
      return registrationId;
    } catch (e) {
      print('获取注册ID异常: $e');
      return null;
    }
  }

  /// 停止推送服务
  static Future<void> stopPushService() async {
    try {
      final result = await JiguangPushPlugin.stopPush();
      if (result != null && result['success'] == true) {
        print('推送服务已停止: ${result['message'] ?? ''}');
      } else {
        print('停止推送失败: ${result?['message'] ?? '未知错误'}');
      }
    } catch (e) {
      print('停止推送异常: $e');
    }
  }

  /// 恢复推送服务
  static Future<void> resumePushService() async {
    try {
      final result = await JiguangPushPlugin.resumePush();
      if (result != null && result['success'] == true) {
        print('推送服务已恢复: ${result['message'] ?? ''}');
      } else {
        print('恢复推送失败: ${result?['message'] ?? '未知错误'}');
      }
    } catch (e) {
      print('恢复推送异常: $e');
    }
  }

  /// 检查推送服务状态
  static Future<bool> isPushServiceStopped() async {
    try {
      final isStopped = await JiguangPushPlugin.isPushStopped();
      return isStopped ?? true;
    } catch (e) {
      print('检查状态异常: $e');
      return true;
    }
  }
}
```

### 在应用启动时初始化

```dart
class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    _initializePush();
  }

  Future<void> _initializePush() async {
    // 初始化推送服务
    await PushService.initializePush();

    // 获取并保存设备注册ID
    final registrationId = await PushService.getDeviceId();
    if (registrationId != null) {
      // 可以将注册ID发送到你的服务器
      // await sendRegistrationIdToServer(registrationId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Push Demo',
      home: MyHomePage(),
    );
  }
}
```

## API 参考

### JiguangPushPlugin

#### 静态方法

| 方法 | 返回类型 | 描述 |
|------|----------|------|
| `initPush()` | `Future<Map<String, dynamic>?>` | 初始化极光推送服务 |
| `getRegistrationId()` | `Future<String?>` | 获取设备注册ID |
| `stopPush()` | `Future<Map<String, dynamic>?>` | 停止推送服务 |
| `resumePush()` | `Future<Map<String, dynamic>?>` | 恢复推送服务 |
| `isPushStopped()` | `Future<bool?>` | 检查推送服务是否停止 |

#### 返回值说明

- `initPush()`, `stopPush()`, `resumePush()` 返回包含以下字段的 Map：
  - `success`: boolean - 操作是否成功
  - `message`: string - 操作结果消息

- `getRegistrationId()` 返回设备的唯一注册ID字符串

- `isPushStopped()` 返回 boolean 值，true 表示推送服务已停止

## 示例项目

查看 `example` 目录中的完整示例项目，了解如何集成和使用所有功能。

运行示例：

```bash
cd example
flutter run
```

## 注意事项

1. **隐私合规**: 在调用 `initPush()` 之前，确保用户已同意隐私政策
2. **网络权限**: 确保应用有网络访问权限
3. **通知权限**: Android 13+ 需要请求通知权限
4. **注册ID**: 注册ID在应用首次安装后生成，卸载重装会改变
5. **类型安全**: 使用返回Map的方法时，请进行null检查和类型安全访问

### 类型安全使用示例

```dart
// ✅ 推荐的安全使用方式
final result = await JiguangPushPlugin.initPush();
if (result != null && result['success'] == true) {
  print('操作成功: ${result['message'] ?? ''}');
} else {
  print('操作失败: ${result?['message'] ?? '未知错误'}');
}

// ❌ 避免直接访问可能为null的值
// print(result['success']); // 可能导致null异常
```

## 故障排除

### 常见问题

1. **初始化失败**
   - 检查 AppKey 是否正确配置
   - 确认网络连接正常
   - 查看 Android 日志输出

2. **收不到推送**
   - 确认推送服务未被停止
   - 检查设备网络连接
   - 验证注册ID是否正确

3. **编译错误**
   - 确保 Android SDK 版本兼容
   - 检查权限配置是否完整

## 更多信息

- [极光推送官方文档](https://docs.jiguang.cn/jpush)
- [Android SDK 集成指南](https://docs.jiguang.cn/jpush/client/Android/android_guide)

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

